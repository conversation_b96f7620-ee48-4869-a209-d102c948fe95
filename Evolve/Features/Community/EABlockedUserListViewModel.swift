import SwiftUI
import SwiftData
import Combine

/// 屏蔽用户列表ViewModel
/// 🔑 批次三新增：管理屏蔽用户列表的业务逻辑
@MainActor
class EABlockedUserListViewModel: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var blockedUsers: [BlockedUserDisplayItem] = []
    @Published var isLoading = false
    
    // MARK: - 依赖注入
    
    var sessionManager: EASessionManager?
    var repositoryContainer: EARepositoryContainer?
    
    // MARK: - 回调
    
    var onError: ((String) -> Void)?
    var onSuccess: ((String) -> Void)?
    
    // MARK: - 方法
    
    /// 加载屏蔽用户列表
    func loadBlockedUsers() async throws {
        await MainActor.run {
            isLoading = true
        }
        
        defer {
            Task { @MainActor in
                isLoading = false
            }
        }
        
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager?.safeCurrentUser else {
            await MainActor.run {
                onError?("用户信息不可用")
            }
            return
        }
            
            let blockingService = repositoryContainer.blockingService
            
            // 获取屏蔽用户列表
            let blockedUserIds = await blockingService.getBlockedUsers(currentUserID: currentUser.id)
            
            // 🔑 修复：使用并发批处理优化性能，限制并发数量
            let displayItems = await withTaskGroup(of: BlockedUserDisplayItem?.self, returning: [BlockedUserDisplayItem].self) { group in
                var results: [BlockedUserDisplayItem] = []

                // 限制并发数量，避免过多请求
                let batchSize = 5
                let batches = blockedUserIds.chunked(into: batchSize)

                for batch in batches {
                    for userId in batch {
                        group.addTask {
                            await self.createDisplayItem(userId: userId)
                        }
                    }

                    // 等待当前批次完成
                    for await result in group {
                        if let item = result {
                            results.append(item)
                        }
                    }
                }

                return results
            }
            
        await MainActor.run {
            blockedUsers = displayItems.sorted { $0.displayName < $1.displayName }
        }
    }
    
    /// 取消屏蔽用户
    /// 🔑 优化修复：使用FriendshipService的统一方法，确保好友关系恢复
    func unblockUser(userId: UUID) async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager?.safeCurrentUser else {
            await MainActor.run {
                onError?("用户信息不可用")
            }
            return
        }

            // 🔑 使用 BlockingService 的取消屏蔽方法
            let blockingService = repositoryContainer.blockingService
            await blockingService.unblockUser(currentUserID: currentUser.id, userID: userId)

            // 🔑 发送状态变化通知，让好友列表自动刷新
            NotificationCenter.default.post(
                name: .blockingStatusChanged,
                object: nil,
                userInfo: ["userId": userId, "action": "unblocked"]
            )

        await MainActor.run {
            // 从屏蔽列表中移除
            blockedUsers.removeAll { $0.userId == userId }
            onSuccess?("已取消屏蔽，好友关系已恢复")
        }
    }
    
    /// 取消屏蔽所有用户
    func unblockAllUsers() async {
        guard let repositoryContainer = repositoryContainer,
              let currentUser = await sessionManager?.safeCurrentUser else {
            await MainActor.run {
                onError?("用户信息不可用")
            }
            return
        }
            
            let blockingService = repositoryContainer.blockingService
            let userIds = blockedUsers.map { $0.userId }
            
            // 批量取消屏蔽
            for userId in userIds {
                await blockingService.unblockUser(currentUserID: currentUser.id, userID: userId)
            }
            
        await MainActor.run {
            blockedUsers.removeAll()
            onSuccess?("已取消屏蔽所有用户")
        }
    }
    
    // MARK: - 私有方法
    
    /// 🔑 新增：创建显示项
    private func createDisplayItem(userId: UUID) async -> BlockedUserDisplayItem? {
        guard let userProfile = await getUserProfile(userId: userId) else {
            return nil
        }

        // TODO: 从屏蔽记录中获取实际屏蔽日期
        let blockedDate = await getBlockedDate(userId: userId) ?? Date()

        return BlockedUserDisplayItem(
            userId: userId,
            displayName: userProfile.user?.username ?? "未知用户",
            avatarData: userProfile.user?.avatarData,
            blockedDate: blockedDate
        )
    }

    /// 🔑 新增：获取屏蔽日期（待实现）
    private func getBlockedDate(userId: UUID) async -> Date? {
        // TODO: 实现从屏蔽记录中获取实际屏蔽日期
        // 当前返回nil，使用默认的当前时间
        return nil
    }

    /// 获取用户档案
    private func getUserProfile(userId: UUID) async -> EAUserSocialProfile? {
        guard let repositoryContainer = repositoryContainer else { return nil }

        // 通过用户Repository获取用户，然后获取其社交档案
        let user = await repositoryContainer.userRepository.fetchUser(by: userId)
        return user?.socialProfile
    }
}

// MARK: - Array扩展

extension Array {
    /// 将数组分割成指定大小的批次
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
