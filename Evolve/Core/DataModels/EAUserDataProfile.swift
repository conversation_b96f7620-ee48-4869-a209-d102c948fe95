import Foundation
import SwiftData

/// 用户数据档案模型 - 管理用户的数据关系
/// 解决EAUser模型关系数量≤5个的限制问题
@Model
final class EAUserDataProfile: @unchecked Sendable {
    var id: UUID = UUID()
    var creationDate: Date = Date()
    var lastUpdateDate: Date = Date()
    
    // 🔗 与核心用户的关系（普通属性，EAUser端定义inverse）
    var user: EAUser?
    
    // 🔗 数据关系1：用户-支付记录（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAPayment.userDataProfile)
    var payments: [EAPayment]
    
    // 🔗 数据关系2：用户-分析记录（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAAnalytics.userDataProfile)
    var analytics: [EAAnalytics]
    
    // 🔗 数据关系3：用户-路径记录（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAPath.userDataProfile)
    var paths: [EAPath]
    
    // 🔗 数据关系4：用户-AI消息（一对多）
    @Relationship(deleteRule: .cascade, inverse: \EAAIMessage.userDataProfile)
    var aiMessages: [EAAIMessage]
    
    // 🔗 数据关系5：预留扩展位置（认证信息已集成到EAUser中）
    // 注意：认证相关信息已直接存储在EAUser模型中，此处预留扩展位置
    
    init() {
        // ✅ 关键修复：iOS 18.2要求关系集合在init中初始化
        self.payments = []
        self.analytics = []
        self.paths = []
        self.aiMessages = []
    }
    
    // MARK: - 便捷访问方法
    
    /// 获取活跃支付记录数量
    func getActivePaymentsCount() -> Int {
        return payments.filter { $0.isActive }.count
    }
    
    /// 获取最近的分析记录
    func getRecentAnalytics(days: Int = 7) -> [EAAnalytics] {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        return analytics.filter { $0.timestamp >= cutoffDate }
    }
    
    /// 获取活跃路径数量
    func getActivePathsCount() -> Int {
        return paths.count // 假设所有路径都是活跃的
    }
    
    /// 获取最近的AI消息
    func getRecentAIMessages(limit: Int = 10) -> [EAAIMessage] {
        return Array(aiMessages.sorted { $0.timestamp > $1.timestamp }.prefix(limit))
    }
    
    /// 清理旧的分析数据
    @MainActor
    func cleanupOldAnalytics(olderThan days: Int, in context: ModelContext) throws {
        guard self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let oldAnalytics = analytics.filter { $0.timestamp < cutoffDate }
        
        for analytics in oldAnalytics {
            context.delete(analytics)
        }
        
        try context.save()
    }
    
    /// 清理旧的AI消息
    @MainActor
    func cleanupOldAIMessages(keepRecent: Int, in context: ModelContext) throws {
        guard self.modelContext == context else {
            throw DataModelError.contextMismatch
        }
        
        let sortedMessages = aiMessages.sorted { $0.timestamp > $1.timestamp }
        let messagesToDelete = Array(sortedMessages.dropFirst(keepRecent))
        
        for message in messagesToDelete {
            context.delete(message)
        }
        
        try context.save()
    }
} 